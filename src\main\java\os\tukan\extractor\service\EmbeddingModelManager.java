package os.tukan.extractor.service;

import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import os.tukan.extractor.config.EmbeddingModelConfig;
import os.tukan.extractor.model.EmbeddingResult;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Log4j2
@Service
public class EmbeddingModelManager {

    private final Map<String, EmbeddingProvider> providerCache = new HashMap<>();

    public EmbeddingModelManager() {
    }

    public float[] generateEmbeddingOnly(String text, EmbeddingModelConfig config) throws Exception {
        EmbeddingProvider provider = getProvider(config);
        return provider.embed(text);
    }

    /**
     * Get an embedding provider for the given model configuration
     * Creates separate provider instances to prevent config overwriting
     */
    public EmbeddingProvider getProvider(EmbeddingModelConfig config) {
        if (config == null) {
            throw new IllegalArgumentException("Embedding model config cannot be null");
        }

        String cacheKey = config.getId();

        if (providerCache.containsKey(cacheKey)) {
            return providerCache.get(cacheKey);
        }

        EmbeddingProvider provider;

        if (config.getProvider().equalsIgnoreCase("ollama")) {
            OllamaEmbeddingProvider ollamaProvider = new OllamaEmbeddingProvider();
            ollamaProvider.setConfig(config);
            provider = ollamaProvider;
        } else {
            throw new IllegalArgumentException("Unsupported embedding provider: " + config.getProvider());
        }

        providerCache.put(cacheKey, provider);
        return provider;
    }


    public boolean testModelConnection(EmbeddingModelConfig config) {
        try {
            EmbeddingProvider provider = getProvider(config);
            return provider.isAvailable();
        } catch (Exception e) {
            return false;
        }
    }

    public EmbeddingResult generateEmbeddingWithTiming(String text, EmbeddingModelConfig config) throws Exception {

        EmbeddingProvider provider = getProvider(config);

        // Enhanced warmup strategy to eliminate all external factors
        performComprehensiveWarmup(provider, text);

        // Measure only the actual embedding computation time with multiple samples for accuracy
        List<Long> timings = new ArrayList<>();
        float[] finalEmbedding = null;

        // Take multiple timing measurements to reduce variance
        for (int i = 0; i < 3; i++) {
            long start = System.nanoTime();
            float[] embedding = provider.embed(text);
            long durationMs = (System.nanoTime() - start) / 1_000_000;
            timings.add(durationMs);

            if (i == 0) {
                finalEmbedding = embedding; // Use first embedding as result
            }
        }

        // Use median timing to reduce impact of outliers
        long medianTime = calculateMedianTiming(timings);

        return new EmbeddingResult(finalEmbedding, medianTime, config.getModelName(), config.getProvider());
    }

    /**
     * Perform comprehensive warmup to eliminate cold start delays, initialization overhead, and network latency
     */
    private void performComprehensiveWarmup(EmbeddingProvider provider, String actualText) throws Exception {
        // Phase 1: Basic warmup calls to eliminate cold start
        for (int i = 0; i < 3; i++) {
            provider.embed("warmup text " + i);
        }

        // Phase 2: Progressive length warmup to eliminate size-related initialization
        String[] warmupTexts = {
            "short",
            "medium length warmup text for testing",
            "longer warmup text that simulates more realistic document content for comprehensive testing"
        };

        for (String warmupText : warmupTexts) {
            provider.embed(warmupText);
        }

        // Phase 3: Similar length warmup to eliminate network latency and caching effects
        String similarLengthText = createSimilarLengthText(actualText);
        provider.embed(similarLengthText);

        // Phase 4: Final warmup with exact length to eliminate any remaining overhead
        String exactLengthText = createExactLengthText(actualText);
        provider.embed(exactLengthText);
    }

    private String createSimilarLengthText(String text) {
        int targetLength = text.length();
        if (targetLength <= 50) {
            return "warmup text with similar length";
        }

        StringBuilder sb = new StringBuilder();
        String baseText = "warmup text ";
        while (sb.length() < targetLength) {
            sb.append(baseText);
        }
        return sb.substring(0, Math.min(targetLength, sb.length()));
    }

    private String createExactLengthText(String text) {
        int targetLength = text.length();
        StringBuilder sb = new StringBuilder();
        String pattern = "x";

        for (int i = 0; i < targetLength; i++) {
            sb.append(pattern);
        }
        return sb.toString();
    }

    private long calculateMedianTiming(List<Long> timings) {
        List<Long> sortedTimings = new ArrayList<>(timings);
        sortedTimings.sort(Long::compareTo);

        int size = sortedTimings.size();
        if (size % 2 == 0) {
            return (sortedTimings.get(size / 2 - 1) + sortedTimings.get(size / 2)) / 2;
        } else {
            return sortedTimings.get(size / 2);
        }
    }


    public void clearCache() {
        providerCache.clear();
    }

}
